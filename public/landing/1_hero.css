/* Hero Section Styles */
.hero-section {
  padding: 20px 0 0 0;
  position: relative;
  z-index: 1;
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 9fr 4fr;
  gap: 20px;
  align-items: stretch;
}

/* Slider Column (3/4) */
.hero-slider-column {
  position: relative;
}

.hero-slider {
  background: rgba(var(--secondary-color), 0.3);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  height: 400px;
  border: 1px solid rgba(var(--primary-color), 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slider-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: grab;
  will-change: transform;
}

.slider-track.dragging {
  transition: none;
  cursor: grabbing;
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  cursor: pointer;
  position: relative;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 20px;
}

/* Mobile optimization for hero slider images */
@media (max-width: 768px) {
  .slide-image {
    object-position: left center;
  }
}

/* Slider Dots */
.slider-dots {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 12px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider-dot.active {
  background: rgb(var(--primary-color));
  border-color: rgb(var(--primary-color));
  transform: scale(1.3);
  box-shadow:
    0 0 16px rgba(var(--primary-color), 0.6),
    0 2px 8px rgba(0, 0, 0, 0.4);
}

.slider-dot:hover {
  background: rgba(var(--primary-color), 0.8);
  border-color: rgba(var(--primary-color), 0.9);
  transform: scale(1.1);
  box-shadow:
    0 0 12px rgba(var(--primary-color), 0.4),
    0 2px 8px rgba(0, 0, 0, 0.3);
}

/* CTA Column (1/4) */
.hero-cta-column {
  position: relative;
  background:
    linear-gradient(145deg, rgba(30, 35, 50, 0.98) 0%, rgba(25, 30, 45, 0.95) 100%);
  border-radius: 20px;
  padding: 28px 20px;
  border: 2px solid #FFD700;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    0 0 20px rgba(255, 215, 0, 0.15);
  overflow: hidden;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

/* CTA Background Effects */
.hero-cta-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(var(--primary-color), 0.06) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(var(--primary-color), 0.04) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
  opacity: 0.8;
}



.cta-content {
  position: relative;
  z-index: 4;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  padding: 12px 8px;
}

.hero-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin: 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.hero-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 400;
  max-width: 260px;
  margin: 0 auto;
}

.hero-cta-button {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.85) 100%);
  border: 1px solid rgba(var(--primary-color), 0.4);
  border-radius: 14px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow:
    0 4px 16px rgba(var(--primary-color), 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 240px;
  margin: 0 auto;
}

.hero-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.25),
    transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.hero-cta-button:hover::before {
  left: 100%;
}

.hero-cta-button:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(var(--primary-color), 0.4),
    0 3px 8px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, rgba(var(--primary-color), 1.1) 0%, rgb(var(--primary-color)) 100%);
  border-color: rgba(var(--primary-color), 0.6);
}

.hero-cta-button:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

.cta-text {
  position: relative;
  z-index: 2;
  font-weight: 700;
}

.cta-icon {
  font-size: 1.3rem;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.hero-cta-button:hover .cta-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Animations */
@keyframes modernGlow {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Loading State */
.hero-slider.loading {
  background: rgba(var(--secondary-color), 0.2);
}

.hero-slider.loading::after {
  content: 'Slaytlar yükleniyor...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  z-index: 5;
}

/* Error State */
.hero-slider.error::after {
  content: 'Slaytlar yüklenemedi';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 100, 100, 0.8);
  font-size: 1.1rem;
  z-index: 5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-container {
    padding: 0 20px;
  }

  .hero-content {
    gap: 20px;
  }

  .hero-slider {
    height: 350px;
  }

  .hero-cta-column {
    padding: 24px 18px;
    border-radius: 18px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 0.95rem;
  }

  .hero-cta-button {
    padding: 15px 28px;
    font-size: 1.05rem;
  }

  .slider-dots {
    bottom: 16px;
    padding: 6px 14px;
  }
}

@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 2fr 1fr;
    gap: 18px;
  }

  .hero-slider {
    height: 320px;
  }

  .hero-cta-column {
    padding: 22px 16px;
    border-radius: 16px;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .hero-description {
    font-size: 0.9rem;
    max-width: 220px;
  }

  .hero-cta-button {
    padding: 14px 24px;
    font-size: 1rem;
    border-radius: 12px;
  }

  .cta-content {
    gap: 16px;
  }

  .user-content {
    gap: 14px;
  }

  .slider-dots {
    bottom: 14px;
    gap: 10px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 15px 0;
  }

  .hero-container {
    padding: 0 16px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .hero-slider {
    height: 280px;
  }

  .hero-cta-column {
    padding: 20px 16px;
    border-radius: 16px;
  }

  .hero-title {
    font-size: 1.6rem;
  }

  .hero-description {
    font-size: 0.85rem;
    line-height: 1.3;
    max-width: 280px;
  }

  .hero-cta-button {
    padding: 14px 28px;
    font-size: 1rem;
    border-radius: 12px;
  }

  .cta-content {
    gap: 18px;
  }

  .user-content {
    gap: 16px;
  }

  .user-status {
    gap: 6px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 5px 10px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 12px 0;
  }

  .hero-container {
    padding: 0 14px;
  }

  .hero-content {
    gap: 18px;
  }

  .hero-slider {
    height: 240px;
    border-radius: 18px;
  }

  .hero-cta-column {
    padding: 22px 18px;
    border-radius: 18px;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .hero-description {
    font-size: 0.85rem;
    max-width: 280px;
  }

  .hero-cta-button {
    padding: 14px 28px;
    font-size: 0.95rem;
    border-radius: 14px;
  }

  .cta-content {
    gap: 20px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 6px 12px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

/* User Content Styles */
.user-content {
  position: relative;
  z-index: 4;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 16px;
  padding: 12px 8px;
}

/* Rank badge positioned in top-right corner */
.user-rank {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.user-welcome {
  display: flex;
  flex-direction: column;
  gap: 16px;
  text-align: left;
  padding-right: 120px; /* Make space for rank badge */
}

.user-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin: 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.user-status {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.rank-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 20px;
  color: #1A1A1A;
  font-weight: 700;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow:
    0 4px 12px rgba(255, 215, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
}

.rank-icon {
  display: none; /* Hide the emoji */
}

.user-vip-status {
  display: flex;
  justify-content: center;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 14px;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border: 1px solid #FFD700;
  border-radius: 20px;
  color: #1A1A1A;
  font-weight: 700;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  box-shadow:
    0 4px 12px rgba(255, 215, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.vip-icon {
  font-size: 1rem;
}

.user-description {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.5;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  font-weight: 400;
  max-width: 280px;
  margin: 0 auto;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.hero-cta-button.primary {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.85) 100%);
}

.hero-cta-button.secondary {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #1A1A1A;
  box-shadow:
    0 4px 12px rgba(255, 215, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.4);
}

.hero-cta-button.secondary:hover {
  background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
  box-shadow:
    0 6px 16px rgba(255, 215, 0, 0.4),
    0 3px 6px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.6);
}



/* Responsive Design for User Content */
@media (max-width: 1024px) {
  .user-title {
    font-size: 1.6rem;
  }

  .user-description {
    font-size: 0.9rem;
    max-width: 220px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.75rem;
    padding: 6px 12px;
  }

  .rank-icon, .vip-icon {
    font-size: 0.9rem;
  }

  .user-actions {
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .user-title {
    font-size: 1.4rem;
  }

  .user-status {
    gap: 6px;
    flex-direction: row;
    justify-content: flex-start;
  }

  .user-description {
    font-size: 0.85rem;
    max-width: 280px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.7rem;
    padding: 6px 10px;
  }

  .user-actions {
    gap: 12px;
  }

  .user-content {
    gap: 16px;
  }

  .user-rank {
    top: 8px;
    right: 8px;
  }

  .user-welcome {
    padding-right: 100px;
  }

  .game-image-container {
    width: 60px;
    height: 60px;
  }

  .game-name {
    font-size: 0.9rem;
  }

  .game-provider {
    font-size: 0.75rem;
  }

  .play-again-text {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .user-title {
    font-size: 1.3rem;
  }

  .user-description {
    font-size: 0.8rem;
    max-width: 260px;
  }

  .user-actions {
    gap: 10px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.65rem;
    padding: 5px 8px;
  }

  .user-content {
    gap: 14px;
  }

  .user-rank {
    top: 6px;
    right: 6px;
  }

  .user-welcome {
    padding-right: 80px;
  }

  .game-image-container {
    width: 55px;
    height: 55px;
  }

  .last-played-game {
    padding: 12px;
    gap: 12px;
  }

  .game-name {
    font-size: 0.85rem;
  }

  .game-provider {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.75rem;
  }
}

/* Last Played Slot Styles */
.last-played-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  width: 100%;
}

.last-played-game {
  display: flex;
  align-items: center;
  gap: 16px;
  background: linear-gradient(145deg, rgba(20, 25, 35, 0.95) 0%, rgba(15, 20, 30, 0.9) 100%);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(8px);
  border: 2px solid #FFD700;
  width: 100%;
  max-width: 280px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.last-played-game:hover {
  transform: translateY(-2px);
  border-color: #FFA500;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 215, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.game-image-container {
  flex-shrink: 0;
  width: 70px;
  height: 70px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(145deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.last-played-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 11px;
  transition: transform 0.3s ease;
}

.last-played-game:hover .last-played-image {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.last-played-game:hover .play-overlay {
  opacity: 1;
}

.play-button {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 12px rgba(255, 215, 0, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
  background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
  transform: scale(1.15);
  box-shadow:
    0 6px 16px rgba(255, 215, 0, 0.5),
    0 3px 6px rgba(0, 0, 0, 0.4);
}

.play-icon {
  color: #1A1A1A;
  font-size: 14px;
  font-weight: bold;
  margin-left: 2px; /* Slight offset to center the triangle visually */
}

.play-again-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.game-info {
  flex: 1;
  min-width: 0;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.game-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.game-provider {
  font-size: 0.85rem;
  color: rgba(255, 215, 0, 0.9);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}



/* Responsive Design for Last Played Slot */
@media (max-width: 1024px) {
  .last-played-game {
    max-width: 260px;
    padding: 10px;
    gap: 10px;
  }

  .game-image-container {
    width: 50px;
    height: 50px;
  }

  .game-name {
    font-size: 0.85rem;
  }

  .game-provider {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.75rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .last-played-section {
    gap: 14px;
    padding: 16px 0;
  }

  .last-played-game {
    max-width: 240px;
    padding: 8px;
  }

  .game-image-container {
    width: 45px;
    height: 45px;
  }

  .game-name {
    font-size: 0.8rem;
  }

  .game-provider {
    font-size: 0.65rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .last-played-section {
    gap: 12px;
    padding: 14px 0;
  }

  .last-played-game {
    max-width: 220px;
    padding: 8px;
  }

  .game-image-container {
    width: 40px;
    height: 40px;
  }

  .game-name {
    font-size: 0.75rem;
  }

  .game-provider {
    font-size: 0.6rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}
