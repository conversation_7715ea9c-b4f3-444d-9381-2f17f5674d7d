// Hero Section Slider JavaScript
window.$LANDING.onLifecycle({ selector: `.makrobet-landing`, page: /^\/\w+$/, onMount: (_, __, kill) => {
  // Configuration
  const SLIDER_TYPE = 34
  const SLIDER_DEVICE_TYPE = window.origin.includes('//m.') ? 2 : 1
  const USE_MOCKED_DATA = false; // Set to false to use API
  const SLIDER_API_URL = `https://pn17.pfnow.net/api/tr/consumer`;
  const IMAGE_BASE_URL = `https://cdn.pfcdn100.com/merchants/pn17/uploads/`;
  const SLIDE_DURATION = 5000; // 5 seconds per slide

  function rafThrottle(fn) {
    let locked = false;
    return (...args) => {
      if (locked) return;
      locked = true;
      requestAnimationFrame(() => {
        fn(...args);
        locked = false;
      });
    };
  }
  function isLoggedIn() {
    return window.localStorage.getItem("LoggedIn") === "true";
  }

  // User data fetching functions
  async function fetchUserData() {
    if (!isLoggedIn()) {
      console.log('User is not logged in');
      return null;
    }

    console.log('User is logged in, fetching user data...');

    const userData = {};

    try {
      // Try the first endpoint
      const response1 = await fetch('https://pn17.pfnow.net/api/fpp/customer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cid: window.localStorage.getItem('customerCode'),
          pft: window.localStorage.getItem('cGFuZWw'),
          token: window.localStorage.getItem('s7oryO9STV')
        })
      });

      if (response1.ok) {
        Object.assign(userData, await response1.json());
        console.log('User data from pn17.pfnow.net:', userData);
      } else {
        console.log('First endpoint failed, trying second endpoint...');
      }
    } catch (error) {
      console.error('Error fetching from first endpoint:', error);
    }

    try {
      // Try the second endpoint using window.location.origin
      const response2 = await fetch(`${window.location.origin}/odin/api/user/getCurrentCustomer`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          's7oryo9stv': window.localStorage.getItem('s7oryO9STV').replace(/^"|"$/g, '')
        },
        credentials: 'include' // Include cookies for authentication
      });

      if (response2.ok) {
        Object.assign(userData, (await response2.json())['data']);
        console.log('User data from getCurrentCustomer:', userData);
        return userData;
      } else {
        console.error('Second endpoint also failed:', response2.status, response2.statusText);
      }
    } catch (error) {
      console.error('Error fetching from second endpoint:', error);
    }

    console.error('Failed to fetch user data from both endpoints');
    return null;
  }

  // Function to check login status and fetch user data
  async function checkUserAndFetchData() {
    const loggedIn = isLoggedIn();
    console.log('Login status:', loggedIn);

    if (loggedIn) {
      const userData = await fetchUserData();
      console.log("USERDATADUDE", userData)
      return {
        isLoggedIn: true,
        userData: userData
      };
    } else {
      return {
        isLoggedIn: false,
        userData: null
      };
    }
  }

  // Last played slot functions
  async function fetchLastPlayedSlot() {
    try {
      // Get current timestamp and calculate 24 hours ago
      const now = Date.now();
      const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);

      const response = await fetch(`${window.location.origin}/odin/api/user/casinoapi/playerGameTransactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          's7oryo9stv': window.localStorage.getItem('s7oryO9STV').replace(/^"|"$/g, '')
        },
        body: JSON.stringify({
          requestBody: {
            startDate: twentyFourHoursAgo,
            endDate: now
          },
          device: 'd',
          languageId: 2
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data && data.data.gameTransactions && data.data.gameTransactions.length > 0) {
        // Get the most recent game transaction
        const lastGame = data.data.gameTransactions[0];
        console.log('Last played game:', lastGame);
        return lastGame;
      } else {
        console.log('No recent game transactions found');
        return null;
      }
    } catch (error) {
      console.error('Error fetching last played slot:', error);
      return null;
    }
  }

  function getGameImageUrl(gameId) {
    return `${window.location.origin}/cdn/common/assets/images/casino/300x200/${gameId}.jpg`
  }

  function displayRandomSlotGame(gameData) {
    if (!gameData) {
      console.log('No random game data to display');
      return;
    }

    const lastPlayedSection = document.getElementById('last-played-section');
    const lastPlayedImage = document.getElementById('last-played-image');
    const lastPlayedName = document.getElementById('last-played-name');
    const lastPlayedProvider = document.getElementById('last-played-provider');
    const playAgainText = document.querySelector('.play-again-text');

    if (!lastPlayedSection) {
      console.warn('Last played section not found');
      return;
    }

    // Add click handler to the entire game block
    const lastPlayedGame = document.getElementById('last-played-game');

    const handleGameClick = () => {
      // Build game URL using the same pattern as popular games
      const navigationPath = `/games/casino/detail/normal/:id`;
      const gameUrl = navigationPath.replace(':id', gameData.gameId || gameData.id);

      if (window.$LANDING && window.$LANDING.navigate) {
        window.$LANDING.navigate(gameUrl);
      } else {
        window.location.href = gameUrl;
      }
    };

    // Update game information
    if (lastPlayedImage) {
      const imageUrl = getGameImageUrl(gameData.gameId || gameData.id);
      lastPlayedImage.src = imageUrl;
      lastPlayedImage.alt = gameData.gameName || gameData.name;

      // Add error handling for image loading
      lastPlayedImage.onerror = function() {
        // Fallback to a default game image or hide the image
        this.style.display = 'none';
      };
    }

    if (lastPlayedName) {
      lastPlayedName.textContent = gameData.gameName || gameData.name;
    }

    if (lastPlayedProvider) {
      lastPlayedProvider.textContent = gameData.vendorName || gameData.provider;
    }

    // Update the text to "Try something new?" in Turkish
    if (playAgainText) {
      playAgainText.textContent = 'Yeni bir şey dene?';
    }

    const lastPlayedBtn = document.getElementById('last-played-btn');
    if (lastPlayedBtn) {
      lastPlayedBtn.setAttribute('data-game-id', gameData.gameId || gameData.id);
      lastPlayedBtn.onclick = handleGameClick;
    }

    if (lastPlayedGame) {
      lastPlayedGame.onclick = handleGameClick;
    }

    // Show the last played section
    lastPlayedSection.style.display = 'flex';
    console.log('Random slot game displayed successfully');
  }

  function displayLastPlayedSlot(gameData) {
    if (!gameData) {
      console.log('No game data to display');
      return;
    }

    const lastPlayedSection = document.getElementById('last-played-section');
    const lastPlayedImage = document.getElementById('last-played-image');
    const lastPlayedName = document.getElementById('last-played-name');
    const lastPlayedProvider = document.getElementById('last-played-provider');
    const lastPlayedBtn = document.getElementById('last-played-btn');
    const playAgainText = document.querySelector('.play-again-text');

    if (!lastPlayedSection) {
      console.warn('Last played section not found');
      return;
    }

    // Update game information
    if (lastPlayedImage) {
      const imageUrl = getGameImageUrl(gameData.gameId);
      lastPlayedImage.src = imageUrl;
      lastPlayedImage.alt = gameData.gameName;

      // Add error handling for image loading
      lastPlayedImage.onerror = function() {
        // Fallback to a default game image or hide the image
        this.style.display = 'none';
      };
    }

    if (lastPlayedName) {
      lastPlayedName.textContent = gameData.gameName;
    }

    if (lastPlayedProvider) {
      lastPlayedProvider.textContent = gameData.vendorName;
    }

    // Update the text to "Play again" in Turkish for last played games
    if (playAgainText) {
      playAgainText.textContent = 'Tekrar oyna';
    }

    // Add click handler to the entire game block
    const lastPlayedGame = document.getElementById('last-played-game');

    const handleGameClick = () => {
      // Build game URL using the same pattern as popular games
      const navigationPath = `/games/casino/detail/normal/:id`;
      const gameUrl = navigationPath.replace(':id', gameData.gameId);

      if (window.$LANDING && window.$LANDING.navigate) {
        window.$LANDING.navigate(gameUrl);
      } else {
        window.location.href = gameUrl;
      }
    };

    if (lastPlayedBtn) {
      lastPlayedBtn.setAttribute('data-game-id', gameData.gameId);
      lastPlayedBtn.onclick = handleGameClick;
    }

    if (lastPlayedGame) {
      lastPlayedGame.onclick = handleGameClick;
    }

    // Show the last played section
    lastPlayedSection.style.display = 'flex';
    console.log('Last played slot displayed successfully');
  }

  function hideLastPlayedSlot() {
    const lastPlayedSection = document.getElementById('last-played-section');
    if (lastPlayedSection) {
      lastPlayedSection.style.display = 'none';
    }
  }

  // Fetch all slot games and pick a random one
  async function fetchRandomSlotGame() {
    try {
      const response = await fetch(`${window.origin}/odin/api/user/casinoapi/getReservedGames`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestBody: {
            currencyId: 1,
            gameType: `casino`
          },
          languageId: 1,
          device: `d`
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data && data.data.games && data.data.games.length > 0) {
        // Pick a random game from the available games
        const randomIndex = Math.floor(Math.random() * data.data.games.length);
        const randomGame = data.data.games[randomIndex];

        console.log('Random slot game selected:', randomGame);
        return randomGame;
      } else {
        console.log('No slot games found');
        return null;
      }
    } catch (error) {
      console.error('Error fetching random slot game:', error);
      return null;
    }
  }

  // User content management functions
  function getRankIcon(rankName) {
    const rankIcons = {
      'BRONZE': '🥉',
      'SILVER': '🥈',
      'GOLD': '🥇',
      'PLATINUM': '💎',
      'DIAMOND': '💠',
      'ELITE': '⭐',
      'MACRO BLACK': '🖤'
    };
    return rankIcons[rankName] || '🥉';
  }

  function getRankColor(rankName) {
    const rankColors = {
      'BRONZE': '#CD7F32',
      'SILVER': '#C0C0C0',
      'GOLD': '#FFD700',
      'PLATINUM': '#E5E4E2',
      'DIAMOND': '#B9F2FF',
      'ELITE': '#FF6B35',
      'MACRO BLACK': '#1A1A1A'
    };
    return rankColors[rankName] || '#CD7F32';
  }

  function getRankGradient(rankName) {
    const rankGradients = {
      'BRONZE': 'linear-gradient(135deg, #CD7F32 0%, #B8860B 100%)',
      'SILVER': 'linear-gradient(135deg, #C0C0C0 0%, #A8A8A8 100%)',
      'GOLD': 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
      'PLATINUM': 'linear-gradient(135deg, #E5E4E2 0%, #D3D3D3 100%)',
      'DIAMOND': 'linear-gradient(135deg, #B9F2FF 0%, #87CEEB 100%)',
      'ELITE': 'linear-gradient(135deg, #FF6B35 0%, #FF4500 100%)',
      'MACRO BLACK': 'linear-gradient(135deg, #1A1A1A 0%, #333333 100%)'
    };
    return rankGradients[rankName] || 'linear-gradient(135deg, #CD7F32 0%, #B8860B 100%)';
  }

  function getRankTextColor(rankName) {
    const textColors = {
      'BRONZE': '#FFFFFF',
      'SILVER': '#1A1A1A',
      'GOLD': '#1A1A1A',
      'PLATINUM': '#1A1A1A',
      'DIAMOND': '#1A1A1A',
      'ELITE': '#FFFFFF',
      'MACRO BLACK': '#FFD700'
    };
    return textColors[rankName] || '#FFFFFF';
  }

  function updateUserContent(userData) {
    if (!userData) {
      console.log('No user data available');
      return;
    }

    // Get DOM elements
    const defaultContent = document.getElementById('cta-content-default');
    const userContent = document.getElementById('cta-content-user');
    const userName = document.getElementById('user-name');
    const userSurname = document.getElementById('user-surname');
    const userRankBadge = document.getElementById('user-rank-badge');
    const userRankIcon = document.getElementById('user-rank-icon');
    const userRankName = document.getElementById('user-rank-name');
    const userVipStatus = document.getElementById('user-vip-status');
    const userVipBadge = document.getElementById('user-vip-badge');
    const vipUpgradeBtn = document.getElementById('vip-upgrade-btn');

    if (!defaultContent || !userContent) {
      console.warn('CTA content elements not found');
      return;
    }

    // Update user name and surname
    if (userName && userData.firstName) {
      userName.textContent = userData.firstName;
    }
    if (userSurname && userData.lastName) {
      userSurname.textContent = userData.lastName;
    }

    // Update rank information
    if (userData.rank_name && userRankBadge && userRankIcon && userRankName) {
      const rankName = userData.rank_name.toUpperCase();
      const rankIcon = getRankIcon(rankName);
      const rankGradient = getRankGradient(rankName);
      const rankTextColor = getRankTextColor(rankName);

      userRankIcon.textContent = rankIcon;
      userRankName.textContent = rankName;
      userRankBadge.style.background = rankGradient;
      userRankBadge.style.color = rankTextColor;
    }

    // Update VIP status
    if (userVipStatus && userVipBadge && vipUpgradeBtn) {
      if (userData.vip === true) {
        // User is VIP - show VIP badge
        userVipStatus.style.display = 'block';
        vipUpgradeBtn.style.display = 'none';
      } else {
        // User is not VIP - hide VIP badge and show upgrade button
        userVipStatus.style.display = 'none';
        vipUpgradeBtn.style.display = 'inline-flex';
      }
    }

    // Fetch and display last played slot
    fetchLastPlayedSlot().then(async lastGame => {
      if (lastGame) {
        displayLastPlayedSlot(lastGame);
      } else {
        // No recent games found, try to fetch a random game
        console.log('No recent games found, fetching random game...');
        const randomGame = await fetchRandomSlotGame();
        if (randomGame) {
          displayRandomSlotGame(randomGame);
        } else {
          hideLastPlayedSlot();
        }
      }
    }).catch(async error => {
      console.error('Error handling last played slot:', error);
      // On error, also try to show a random game as fallback
      const randomGame = await fetchRandomSlotGame();
      if (randomGame) {
        displayRandomSlotGame(randomGame);
      } else {
        hideLastPlayedSlot();
      }
    });

    // Switch content visibility
    defaultContent.style.display = 'none';
    userContent.style.display = 'flex';

    console.log('User content updated successfully');
  }

  function showDefaultContent() {
    const defaultContent = document.getElementById('cta-content-default');
    const userContent = document.getElementById('cta-content-user');

    if (defaultContent && userContent) {
      defaultContent.style.display = 'flex';
      userContent.style.display = 'none';
      // Hide last played slot for non-logged users
      hideLastPlayedSlot();
      console.log('Showing default content for non-logged users');
    }
  }


  // Mocked slider data
  const MOCKED_SLIDES = [
    {
      id: 1,
      path: `/cdn/makrobet/upload_files/100cevrimsiz.png`,
      url: `/contents/promotions`,
      content: `%100 Çevrimsiz Bonus`,
      type: 1
    },
    {
      id: 2,
      path: `/cdn/makrobet/upload_files/anlikkayip.png`,
      url: `/contents/promotions`,
      content: `Anlık Kayıp Bonusu`,
      type: 1
    },
    {
      id: 3,
      path: `/cdn/makrobet/upload_files/deneme250.png`,
      url: `/contents/promotions`,
      content: `250 TL Deneme Bonusu`,
      type: 1
    },
    {
      id: 4,
      path: `/cdn/makrobet/upload_files/haftalikdc.png`,
      url: `/contents/promotions`,
      content: `Haftalık Discount`,
      type: 1
    },
    {
      id: 5,
      path: `/cdn/makrobet/upload_files/WEBSLIDERFIFA.png`,
      url: `/contents/promotions`,
      content: `FIFA`,
      type: 1
    },
    {
      id: 6,
      path: `/cdn/makrobet/upload_files/klasiklere-donus-920x400.png`,
      url: `/contents/promotions`,
      content: `klasiklere douns`,
      type: 1
    },
    {
      id: 7,
      path: `/cdn/makrobet/upload_files/pragmatic-920x400.png`,
      url: `/contents/promotions`,
      content: `pragmatic`,
      type: 1
    },
  ];

  // State
  let slides = [];
  let currentSlideIndex = 0;
  let slideInterval = null;
  let isTransitioning = false;

  // Drag state
  let isDragging = false;
  let startX = 0;
  let currentX = 0;
  let dragOffset = 0;
  let sliderTrack = null;
  let hasDragged = false;

  // Touch gesture detection state
  let touchStartX = 0;
  let touchStartY = 0;
  let gestureDetected = false;
  let isVerticalGesture = false;
  let touchStartedOnSlider = false; // Track if touch started on slider
  const GESTURE_THRESHOLD = 10; // Minimum movement to detect gesture direction

  // DOM Elements
  const sliderContainer = document.querySelector(`.slider-container`);
  const dotsContainer = document.querySelector(`.slider-dots`);
  const heroSlider = document.querySelector(`.hero-slider`);



  // API Functions
  async function fetchSlides() {
    try {
      console.log(`API\`den slaytlar alınıyor...`);
      
      const response = await fetch(SLIDER_API_URL, {
        method: `GET`,
        headers: {
          [`Content-Type`]: `application/json`,
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.sliders || !Array.isArray(data.sliders)) {
        throw new Error(`Invalid response format`);
      }

      return data.sliders.filter(s => s.type === SLIDER_TYPE && s.m_t === SLIDER_DEVICE_TYPE);
    } catch (error) {
      console.error(`Slaytlar alınırken hata:`, error);
      throw error;
    }
  }

  // Slide Management
  function createSlideElement(slide, index) {
    const slideDiv = document.createElement(`div`);
    slideDiv.className = `slide`;
    slideDiv.id = `slide-${index}`;

    const img = document.createElement(`img`);
    // Handle both mocked data (full path) and API data (path only)
    if (USE_MOCKED_DATA || slide.path.startsWith(`/`)) {
      img.src = slide.path;
    } else {
      // img.src = `${IMAGE_BASE_URL}${slide.path}`;
      img.src = slide.path.startsWith('http') ? slide.path : `${IMAGE_BASE_URL}${slide.path}`;
    }
    img.alt = slide.content || `Slayt ${index + 1}`;
    img.className = `slide-image`;

    // Add click handler if URL is provided
    if (slide.url) {
      slideDiv.style.cursor = `pointer`;

      let isScrolling = false;
      let touchStartY = 0;

      const handleSlideNavigation = (e) => {
        // Prevent navigation if user has dragged
        if (hasDragged) {
          e.preventDefault();
          e.stopPropagation();
          return;
        }

        if (slide.url.startsWith(`/`)) {
          $LANDING.navigate(slide.url);
        } else {
          window.open(slide.url, `_blank`);
        }
      };
      const rafThrottledHandleSlideNavigation = rafThrottle(handleSlideNavigation);

      slideDiv.addEventListener(`click`, rafThrottledHandleSlideNavigation);

      // Add touch support with scroll detection
      slideDiv.addEventListener(`touchstart`, (e) => {
        isScrolling = false;
        touchStartY = e.touches[0].clientY;
      });

      slideDiv.addEventListener(`touchmove`, (e) => {
        const touchMoveY = e.touches[0].clientY;
        const deltaY = Math.abs(touchMoveY - touchStartY);

        if (deltaY > 10) {
          isScrolling = true;
        }
      });

      slideDiv.addEventListener(`touchend`, (e) => {
        if (isScrolling) {
          return;
        }

        e.preventDefault();
        rafThrottledHandleSlideNavigation(e);
      });
    }

    slideDiv.appendChild(img);
    return slideDiv;
  }

  function createDotElement(index) {
    const dot = document.createElement(`div`);
    dot.className = `slider-dot`;
    dot.setAttribute(`data-slide-index`, index);

    // Add click handler with proper event handling
    let isScrolling = false;
    let touchStartY = 0;

    const handleDotClick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log(`Dot clicked for slide ${index}`);
      goToSlide(index);
    };

    dot.addEventListener(`click`, handleDotClick);

    // Add touch support with scroll detection
    dot.addEventListener(`touchstart`, (e) => {
      isScrolling = false;
      touchStartY = e.touches[0].clientY;
    });

    dot.addEventListener(`touchmove`, (e) => {
      const touchMoveY = e.touches[0].clientY;
      const deltaY = Math.abs(touchMoveY - touchStartY);

      if (deltaY > 10) {
        isScrolling = true;
      }
    });

    dot.addEventListener(`touchend`, (e) => {
      if (isScrolling) {
        return;
      }

      e.preventDefault();
      handleDotClick(e);
    });

    // Add keyboard support
    dot.setAttribute(`tabindex`, `0`);
    dot.setAttribute(`role`, `button`);
    dot.setAttribute(`aria-label`, `${index + 1}. slayta git`);

    dot.addEventListener(`keydown`, (e) => {
      if (e.key === `Enter` || e.key === ` `) {
        e.preventDefault();
        goToSlide(index);
      }
    });

    return dot;
  }

  function renderSlides() {
    if (!slides.length) return;

    console.log(`${slides.length} slayt oluşturuluyor...`);

    // Clear existing slides and reset state
    sliderContainer.innerHTML = ``;
    dotsContainer.innerHTML = ``;
    currentSlideIndex = 0;
    isTransitioning = false;

    // Create slider track
    sliderTrack = document.createElement(`div`);
    sliderTrack.className = `slider-track`;
    sliderContainer.appendChild(sliderTrack);

    // Create slides and dots
    slides.forEach((slide, index) => {
      const slideElement = createSlideElement(slide, index);
      sliderTrack.appendChild(slideElement);

      const dotElement = createDotElement(index);
      dotsContainer.appendChild(dotElement);
    });

    console.log(`${slides.length} slayt ve ${dotsContainer.children.length} nokta oluşturuldu`);

    // Initialize slider position
    rafThrottledUpdateSliderPosition();

    // Setup drag functionality
    setupDragHandlers();
  }

  function updateSliderPosition() {
    if (!sliderTrack || slides.length === 0) return;

    const translateX = -currentSlideIndex * 100;
    sliderTrack.style.transform = `translateX(${translateX}%)`;

    // Update dots
    const allDots = dotsContainer.querySelectorAll(`.slider-dot`);
    allDots.forEach((dot, i) => {
      if (i === currentSlideIndex) {
        dot.classList.add(`active`);
      } else {
        dot.classList.remove(`active`);
      }
    });
  }

  const rafThrottledUpdateSliderPosition = rafThrottle(updateSliderPosition);

  function goToSlide(index) {
    if (isTransitioning || index === currentSlideIndex || index < 0 || index >= slides.length) return;

    console.log(`Switching from slide ${currentSlideIndex} to slide ${index}`);

    isTransitioning = true;
    currentSlideIndex = index;

    rafThrottledUpdateSliderPosition();

    // Reset auto-slide timer
    startAutoSlide();

    // Reset transition flag after animation
    setTimeout(() => {
      isTransitioning = false;
      console.log(`Slide transition completed. Current slide: ${currentSlideIndex}`);
    }, 600);
  }

  function nextSlide() {
    if (slides.length === 0) return;

    const nextIndex = (currentSlideIndex + 1) % slides.length;
    console.log(`Auto-advancing to slide ${nextIndex}`);
    goToSlide(nextIndex);
  }

  function setupDragHandlers() {
    if (!sliderTrack) return;

    // Mouse events
    sliderTrack.addEventListener('mousedown', rafThrottledHandleDragStart);
    document.addEventListener('mousemove', rafThrottledHandleDragMove);
    document.addEventListener('mouseup', rafThrottledHandleDragEnd);

    // Touch events with gesture detection
    sliderTrack.addEventListener('touchstart', rafThrottledHandleTouchStart);
    heroSlider.addEventListener('touchmove', rafThrottledHandleTouchMove, { passive: false });
    heroSlider.addEventListener('touchend', rafThrottledHandleTouchEnd);

    // Prevent default drag behavior on images
    sliderTrack.addEventListener('dragstart', (e) => e.preventDefault());
  }

  function getEventX(e) {
    return e.type.includes('mouse') ? e.clientX : e.touches[0].clientX;
  }

  function handleDragStart(e) {
    if (isTransitioning) return;

    isDragging = true;
    hasDragged = false;
    startX = getEventX(e);
    currentX = startX;
    dragOffset = 0;

    sliderTrack.classList.add('dragging');
    stopAutoSlide();
  }
  const rafThrottledHandleDragStart = rafThrottle(handleDragStart);

  function handleDragMove(e) {
    if (!isDragging) return;

    currentX = getEventX(e);
    dragOffset = currentX - startX;

    // Mark that user has dragged if they moved more than a few pixels
    if (Math.abs(dragOffset) > 5) {
      hasDragged = true;
    }

    // Calculate the current position with drag offset
    const baseTranslateX = -currentSlideIndex * 100;
    const dragPercentage = (dragOffset / sliderContainer.offsetWidth) * 100;
    const newTranslateX = baseTranslateX + dragPercentage;

    sliderTrack.style.transform = `translateX(${newTranslateX}%)`;

    e.preventDefault();
  }
  const rafThrottledHandleDragMove = rafThrottle(handleDragMove);

  function handleDragEnd(e) {
    if (!isDragging) return;

    isDragging = false;
    sliderTrack.classList.remove('dragging');

    const threshold = sliderContainer.offsetWidth * 0.2; // 20% threshold
    let newIndex = currentSlideIndex;

    if (Math.abs(dragOffset) > threshold) {
      if (dragOffset > 0 && currentSlideIndex > 0) {
        // Dragged right, go to previous slide
        newIndex = currentSlideIndex - 1;
      } else if (dragOffset < 0 && currentSlideIndex < slides.length - 1) {
        // Dragged left, go to next slide
        newIndex = currentSlideIndex + 1;
      }
    }

    // Reset drag state
    dragOffset = 0;

    if (newIndex !== currentSlideIndex) {
      goToSlide(newIndex);
    } else {
      // Snap back to current position
      rafThrottledUpdateSliderPosition();
      startAutoSlide();
    }

    // Reset hasDragged flag after a short delay to prevent click events
    setTimeout(() => {
      hasDragged = false;
    }, 100);

    // Reset gesture detection state
    gestureDetected = false;
    isVerticalGesture = false;
    touchStartedOnSlider = false;

    e.preventDefault();
  }
  const rafThrottledHandleDragEnd = rafThrottle(handleDragEnd);

  // Touch event handlers with gesture detection
  function handleTouchStart(e) {
    if (isTransitioning || e.touches.length !== 1) return;

    // Initialize touch tracking
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
    gestureDetected = false;
    isVerticalGesture = false;
    touchStartedOnSlider = true;

    // Start as horizontal gesture by default - but don't prevent default yet
    // e.preventDefault(); // Don't block scroll on touchstart
    handleDragStart(e);
  }
  const rafThrottledHandleTouchStart = rafThrottle(handleTouchStart);

  function handleTouchMove(e) {
    // Only handle touch events that started on the slider
    if (e.touches.length === 1 && touchStartedOnSlider) {
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;

      // If we haven't detected gesture direction yet
      if (!gestureDetected) {
        const deltaX = Math.abs(currentX - touchStartX);
        const deltaY = Math.abs(currentY - touchStartY);

        // Check if movement exceeds threshold
        if (deltaX > GESTURE_THRESHOLD || deltaY > GESTURE_THRESHOLD) {
          gestureDetected = true;
          isVerticalGesture = deltaY > deltaX;

          // If vertical gesture detected, stop dragging and allow scrolling
          if (isVerticalGesture) {
            if (isDragging) {
              handleDragEnd(e);
            }
            touchStartedOnSlider = false; // Stop handling this touch sequence
            return; // Don't prevent default, allow vertical scrolling
          }
        }
      }

      // If we're in horizontal drag mode (not vertical), continue dragging
      if (isDragging && !isVerticalGesture) {
        e.preventDefault();
        handleDragMove(e);
      }
    }
  }
  const rafThrottledHandleTouchMove = rafThrottle(handleTouchMove);

  function handleTouchEnd(e) {
    // Only handle if touch started on slider
    if (touchStartedOnSlider) {
      if (isDragging && !isVerticalGesture) {
        e.preventDefault();
        handleDragEnd(e);
      }

      // Reset gesture detection state
      gestureDetected = false;
      isVerticalGesture = false;
      touchStartedOnSlider = false;
    }
  }
  const rafThrottledHandleTouchEnd = rafThrottle(handleTouchEnd);

  function startAutoSlide() {
    // Clear any existing interval
    if (slideInterval) {
      clearInterval(slideInterval);
      slideInterval = null;
    }

    // Only start auto-slide if we have multiple slides
    if (slides.length > 1) {
      slideInterval = setInterval(nextSlide, SLIDE_DURATION);
      console.log(`Auto-slide started with ${SLIDE_DURATION}ms interval`);
    }
  }

  function stopAutoSlide() {
    if (slideInterval) {
      clearInterval(slideInterval);
      slideInterval = null;
    }
  }

  // Loading States
  function setLoadingState() {
    heroSlider.classList.add(`loading`);
    heroSlider.classList.remove(`error`);
  }

  function setErrorState() {
    heroSlider.classList.add(`error`);
    heroSlider.classList.remove(`loading`);
  }

  function clearStates() {
    heroSlider.classList.remove(`loading`, `error`);
  }

  // Initialization
  async function initializeSlider() {
    try {
      setLoadingState();

      let slidesData;

      if (USE_MOCKED_DATA) {
        // Use mocked data
        console.log(`Sahte slayt verileri kullanılıyor`);
        slidesData = MOCKED_SLIDES;
      } else {
        // Fetch from API
        slidesData = await fetchSlides();
      }

      if (!slidesData.length) {
        throw new Error(`Slayt bulunamadı`);
      }

      slides = slidesData;
      clearStates();
      renderSlides();
      startAutoSlide();

      console.log(`Hero slider başarıyla başlatıldı`);

    } catch (error) {
      console.error(`Hero slider başlatılamadı:`, error);
      setErrorState();
    }
  }

  // Event Listeners
  function setupEventListeners() {
    // Pause auto-slide on hover
    if (heroSlider) {
      heroSlider.addEventListener(`mouseenter`, stopAutoSlide);
      heroSlider.addEventListener(`mouseleave`, startAutoSlide);
    }

    // Handle visibility change
    document.addEventListener(`visibilitychange`, () => {
      if (document.hidden) {
        stopAutoSlide();
      } else {
        startAutoSlide();
      }
    });

    // Setup navigation handlers for CTA buttons
    setupCTANavigationHandlers();
  }

  function setupCTANavigationHandlers() {
    // Handle all buttons with data-navigate attribute
    const ctaButtons = document.querySelectorAll('.hero-cta-button[data-navigate]');

    ctaButtons.forEach(button => {
      let isScrolling = false;
      let touchStartY = 0;

      const handleNavigation = (e) => {
        e.preventDefault();
        const path = button.getAttribute('data-navigate');

        if (path && window.$LANDING && window.$LANDING.navigate) {
          window.$LANDING.navigate(path);
        } else if (path) {
          window.location.href = path;
        }
      };

      // Mouse click
      button.addEventListener('click', handleNavigation);

      // Touch support with scroll detection
      button.addEventListener('touchstart', (e) => {
        isScrolling = false;
        touchStartY = e.touches[0].clientY;
      });

      button.addEventListener('touchmove', (e) => {
        const touchMoveY = e.touches[0].clientY;
        const deltaY = Math.abs(touchMoveY - touchStartY);

        if (deltaY > 10) {
          isScrolling = true;
        }
      });

      button.addEventListener('touchend', (e) => {
        if (isScrolling) {
          return;
        }

        e.preventDefault();
        handleNavigation(e);
      });
    });
  }

  // Start initialization
  async function initialize() {
    if (!sliderContainer || !dotsContainer || !heroSlider) {
      console.warn(`Hero slider elements not found`);
      return;
    }

    // Check user login status and fetch data
    const userInfo = await checkUserAndFetchData();
    console.log('User information:', userInfo);

    // Update CTA content based on user status
    if (userInfo.isLoggedIn && userInfo.userData) {
      updateUserContent(userInfo.userData);
    } else {
      showDefaultContent();
    }

    setupEventListeners();
    initializeSlider();
  }

  // Initialize when DOM is ready
  initialize();

  window.addEventListener('@makrobet/unload/landing', () => {
    kill()
    stopAutoSlide()
  }, { once: true })
  
  return () => {
    stopAutoSlide()
  }
}});
